-- Fix commission_balances constraint issue
-- This script removes the problematic check_sufficient_balance constraint
-- and ensures commission conversions can complete successfully

-- Step 1: Check if the constraint exists
SELECT 
    constraint_name,
    check_clause
FROM information_schema.check_constraints 
WHERE table_name = 'commission_balances' 
AND constraint_name LIKE '%sufficient%';

-- Step 2: Drop the problematic constraint if it exists
DO $$ 
BEGIN
    -- Try to drop the constraint if it exists
    IF EXISTS (
        SELECT 1 
        FROM information_schema.table_constraints 
        WHERE table_name = 'commission_balances' 
        AND constraint_name = 'check_sufficient_balance'
    ) THEN
        ALTER TABLE commission_balances 
        DROP CONSTRAINT check_sufficient_balance;
        RAISE NOTICE 'Dropped check_sufficient_balance constraint';
    ELSE
        RAISE NOTICE 'check_sufficient_balance constraint does not exist';
    END IF;
END $$;

-- Step 3: Ensure commission_balances table has correct structure
ALTER TABLE commission_balances 
ADD COLUMN IF NOT EXISTS escrowed_amount DECIMAL(15,2) DEFAULT 0.00;

ALTER TABLE commission_balances 
ADD COLUMN IF NOT EXISTS total_withdrawn DECIMAL(15,2) DEFAULT 0.00;

-- Step 4: Update any NULL values to 0
UPDATE commission_balances 
SET 
    usdt_balance = COALESCE(usdt_balance, 0.00),
    share_balance = COALESCE(share_balance, 0.00),
    total_earned_usdt = COALESCE(total_earned_usdt, 0.00),
    total_earned_shares = COALESCE(total_earned_shares, 0.00),
    escrowed_amount = COALESCE(escrowed_amount, 0.00),
    total_withdrawn = COALESCE(total_withdrawn, 0.00);

-- Step 5: Add a more reasonable constraint that allows proper commission operations
-- This constraint ensures balances can't go negative, but allows escrow operations
ALTER TABLE commission_balances 
ADD CONSTRAINT check_non_negative_balances 
CHECK (
    usdt_balance >= 0 AND 
    share_balance >= 0 AND 
    total_earned_usdt >= 0 AND 
    total_earned_shares >= 0 AND
    escrowed_amount >= 0 AND
    total_withdrawn >= 0
);

-- Step 6: Show current table structure
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name = 'commission_balances' 
ORDER BY ordinal_position;

-- Step 7: Show any remaining constraints
SELECT 
    constraint_name,
    constraint_type
FROM information_schema.table_constraints 
WHERE table_name = 'commission_balances';

COMMENT ON CONSTRAINT check_non_negative_balances ON commission_balances IS 
'Ensures all balance fields are non-negative while allowing proper commission escrow operations';
