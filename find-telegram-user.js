const { db } = require('./src/database/supabase-client');

async function findTelegramUserForUserId4() {
  console.log('🔍 Finding which Telegram account corresponds to user_id = 4...\n');

  try {
    // Get telegram user for user_id = 4
    const { data: telegramUser, error: telegramError } = await db.client
      .from('telegram_users')
      .select('*')
      .eq('user_id', 4)
      .single();

    if (telegramError) {
      console.log('❌ Error finding telegram user:', telegramError.message);
      
      // Check if user_id = 4 exists in users table
      const { data: user, error: userError } = await db.client
        .from('users')
        .select('*')
        .eq('id', 4)
        .single();
      
      if (userError) {
        console.log('❌ User ID 4 does not exist in users table:', userError.message);
      } else {
        console.log('✅ User ID 4 exists in users table:', user);
        console.log('❌ But no corresponding telegram_users record found');
        console.log('💡 This means user_id 4 was created directly in database, not through Telegram bot');
      }
    } else {
      console.log('✅ Found Telegram user for user_id = 4:');
      console.log(`   Telegram ID: ${telegramUser.telegram_id}`);
      console.log(`   Username: @${telegramUser.username}`);
      console.log(`   First Name: ${telegramUser.first_name}`);
      console.log(`   Last Name: ${telegramUser.last_name}`);
      console.log(`   Is Registered: ${telegramUser.is_registered}`);
    }

    // Also check what referrals exist
    console.log('\n📊 Checking all referrals in database...');
    const { data: allReferrals, error: refError } = await db.client
      .from('referrals')
      .select('*')
      .order('created_at', { ascending: false });

    if (refError) {
      console.log('❌ Error fetching referrals:', refError.message);
    } else {
      console.log(`✅ Found ${allReferrals.length} total referrals:`);
      allReferrals.forEach((ref, index) => {
        console.log(`   ${index + 1}. Referrer ID: ${ref.referrer_id}, Referred ID: ${ref.referred_id}, Status: ${ref.status}`);
      });
    }

    // Check current TTTFOUNDER mapping
    console.log('\n🔍 Current TTTFOUNDER mapping:');
    const { data: tttfounderTelegram, error: tttError } = await db.client
      .from('telegram_users')
      .select('*')
      .eq('telegram_id', 1393852532)
      .single();

    if (tttError) {
      console.log('❌ Error finding TTTFOUNDER telegram record:', tttError.message);
    } else {
      console.log(`✅ TTTFOUNDER (@${tttfounderTelegram.username}) maps to user_id: ${tttfounderTelegram.user_id}`);
    }

  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

findTelegramUserForUserId4();
