# Aureus Alliance Holdings Telegram Bot

## Overview

The **Aureus Alliance Bot** (@AureusAllianceBot) is a comprehensive Telegram bot that serves as the primary interface for the Aureus Alliance Holdings equity share purchase platform. It provides a complete share purchase management system directly within Telegram, allowing users to purchase equity shares, manage their portfolios, and interact with the mining share purchase opportunity.

## Bot Configuration

- **Bot Username**: @AureusAllianceBot
- **Bot Token**: 7858706839:AAFRXBSlREW0wPvIyI57uFpHfYopi2CY464
- **Database**: Supabase

## Database Integration

- **Database**: Supabase
- **Project ID**: fgubaqoftdeefcakejwu
- **service_role**: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.9Dl-TPeiRTZI7NrsbISgl50XYrWxzNx0Ffk-TNXWhOA
- **Database Password**: D$4#wTr3VqECr6q

### **Shared Tables**
Both the web application and Telegram bot use the same MySQL database:
- `users` - Main user accounts
- `telegram_users` - Telegram-specific user data
- `aureus_share_purchases` - Share Purchase records
- `crypto_payment_transactions` - Payment tracking
- `company_wallets` - Dynamic wallet addresses
- `commissions` - Referral tracking
- `share_packages` - Package definitions
- `share_purchase_phases` - Phase management

## Core Functionality

### 1. User Authentication & Registration

**Seamless Account Integration**:
- Automatic Telegram-email account linking
- One-time login with permanent session persistence
- Password reset functionality via email
- Session persistence across bot restarts
- Registration flow with email verification

**Authentication Flow**:
1. User starts bot with `/start`
2. Bot prompts for email address
3. User enters existing email or registers new account
4. Password verification or account creation
5. Permanent linking between Telegram ID and email account
6. Auto-login on subsequent interactions

### 2. Share Packages System

**Pre-defined Share Packages**:
- **Shovel**: $25 (5 shares)
- **Pick**: $50 (10 shares)
- **Miner**: $75 (15 shares)
- **Loader**: $100 (20 shares)
- **Excavator**: $250 (50 shares)
- **Crusher**: $500 (100 shares)
- **Refinery**: $750 (150 shares)
- **Aureus**: $1000 (200 shares)

**20-Phase Share Purchase System**:
- **Pre Sale**: $5/share (200,000 shares available)
- **Phase 1-9**: $10-$50/share (increments of $5)
- **Phase 10**: $100/share
- **Phase 11-19**: $200-$1000/share (increments of $100)
- Automatic phase progression when limits reached
- Real-time phase availability tracking

### 3. Custom Share Purchase Feature

**Flexible Share Purchase Amounts**:
- Users can buy shares any amount from $25-$10,000
- Automatic optimal package combination algorithm
- Prioritizes larger packages for efficiency
- Clear breakdown of package combinations
- Confirmation before processing

**Smart Package Selection**:
- Algorithm selects most efficient package combinations
- Example: $275 = 1×Excavator ($250) + 1×Shovel ($25)
- Shows exact share calculations
- Displays total shares and cost breakdown

### 4. Payment Processing System

**Supported Cryptocurrencies**:
- **BSC USDT** (Binance Smart Chain)
- **POL USDT** (Polygon Network)
- **TRON USDT** (Tron Network)

**3-Step Payment Verification**:
1. **Sender Wallet Address**: User provides sending wallet
2. **Payment Screenshot**: Upload transaction screenshot
3. **Transaction Hash**: Provide blockchain transaction hash

**Payment Features**:
- Dynamic wallet address fetching from API
- Clickable wallet addresses for easy copying
- Payment amount display in wallet copy messages
- Screenshot upload and storage
- Admin approval workflow
- Duplicate transaction prevention

### 5. Terms & Conditions System

**Comprehensive Legal Framework**:
- **General Terms**: Platform usage terms
- **Privacy Policy**: Data protection and usage
- **Share Purchase Risks**: Financial risk disclosures
- **Mining Operations**: Mining-specific terms
- **NFT Terms**: 12-month trading restrictions, $1000 minimum value
- **Dividend Policy**: Quarterly distribution terms

**Acceptance Tracking**:
- Individual acceptance of each terms category
- Database logging of acceptance timestamps
- Required before any share purchase processing
- Version control for terms updates

### 6. Mining Calculator & Projections

**Production Calculations**:
- Based on 10 washplants (200 tons/hour each) by June 2026
- Scaling to 57 washplants mining 15 tons gold/year at stage 20
- Operations efficiency: 10 hours per day
- Current gold price integration (~$107k/kg)
- 45% operational costs factored in

**User-Friendly Calculator**:
- Users select share quantity
- Shows dividend calculations based on production
- 5-year growth projections
- Annual profit estimates per share
- Real-time gold price API integration

### 7. Admin Management System

**Admin Security**:
- <NAME_EMAIL>
- Username-based access control (TTTFOUNDER)
- Two-factor authentication flow
- Session management and timeouts

**Admin Features**:
- **Payment Confirmations**: Review and approve payments
- **User Management**: Search, view, and manage user accounts
- **Communication System**: Direct messaging with users
- **Password Reset Approvals**: Admin-controlled password resets
- **Audit Logging**: Comprehensive action tracking
- **User Search**: By email, Telegram ID, or username

### 8. Referral System

**Commission Structure**:
- 15% commission on all investments (not 3-level MLM)
- Username-based referral identification
- Optional referral registration during auth
- Permanent sponsor relationships
- Dedicated commissions tracking table

**Referral Features**:
- Telegram username as referral identifier
- Inline keyboard confirmations
- Commission status tracking (pending/approved/paid)
- Native Telegram integration (no external dependencies)

### 9. Portfolio & Share Purchase Tracking

**Share Purchase History**:
- Complete transaction history
- Payment status tracking
- Share balance calculations
- Dividend projections
- Share Purchase performance metrics

**Real-time Updates**:
- Phase progression notifications
- Payment confirmations
- Admin communications
- System announcements

## Technical Architecture

### Technology Stack

**Bot Framework**:
- **Language**: Node.js (JavaScript)
- **Framework**: Telegraf.js (Telegram Bot API wrapper)
- **Database**: MySQL (shared with main application)
- **File Storage**: Local screenshot storage
- **API Integration**: Company wallet API

### Database Integration

**Connection Details**:
- Host: localhost:3506 (custom XAMPP port)
- Database: aureus_angels
- Shared tables with web application
- Real-time data synchronization
- Connection pooling for performance

### Security Features

**Data Protection**:
- Encrypted password storage
- Secure session management
- Input validation and sanitization
- SQL injection prevention
- Rate limiting on sensitive operations

**Admin Security**:
- Multi-layer authentication
- Action logging and audit trails
- Username-based restrictions
- Session timeout management
- Privilege escalation prevention

### File Management

**Screenshot Storage**:
- Organized file structure: `/screenshots/`
- Unique filename generation
- File size and type validation
- Automatic cleanup procedures
- Secure file access controls

### Error Handling

**Robust Error Management**:
- Comprehensive error logging
- User-friendly error messages
- Automatic retry mechanisms
- Fallback procedures for API failures
- Debug logging for troubleshooting

## User Experience Features

### Intuitive Navigation

**Button-Based Interface**:
- No slash commands required for regular users
- Inline keyboard menus throughout
- Contextual navigation options
- Clear action confirmations
- Progress indicators for multi-step processes

**Smart State Management**:
- Persistent user states across sessions
- Context-aware responses
- Automatic state cleanup
- Conflict resolution between different flows

### Professional Presentation

**Rich Message Formatting**:
- Markdown formatting for clarity
- Emoji icons for visual appeal
- Structured information display
- Clear call-to-action buttons
- Professional terminology (equity vs share purchase)

**Real-time Feedback**:
- Instant confirmation messages
- Progress updates during processing
- Clear status indicators
- Helpful error messages with next steps

## Business Model Integration

### Equity-Based Language

**Professional Terminology**:
- "Purchasing company equity shares" (not investing)
- "Shareholder" and "equity holder" terminology
- Company name: "Aureus Alliance Holdings"
- Focus on equity ownership rather than returns

### Mining Operations Focus

**Production-Based Calculations**:
- 3,200 KG gold annually at full capacity
- 45% operational costs
- 1,400,000 total shares available
- Dynamic dividend calculations
- Timeline: Full capacity by June 2026

### NPO Integration

**Charitable Component**:
- 10% of payments support 28 NPOs worldwide
- Transparent fund allocation display
- Social impact messaging
- Community engagement features

## Key Bot Commands & Features

### User Commands (Button-Based Interface)

**Main Menu Options**:
- 📦 **Share Packages** - View and purchase pre-defined packages
- 💰 **Custom Share Purchase** - Buy Shares any amount with smart package selection
- 📊 **Mining Calculator** - Calculate dividend projections
- 👥 **Referral System** - Manage referrals and commissions
- 📱 **Portfolio** - View share purchase history and performance

**Admin Commands** (Restricted to TTTFOUNDER):
- `/admin` - Access admin panel
- `/users` - User management
- `/payments` - Payment confirmations
- `/messages` - User communication system

## Deployment & System Requirements

### Current Deployment

**System Configuration**:
- **File Location**: `c:\xampp\htdocs\Aureus 1 - Complex\telegram-bot.cjs`
- **Runtime**: Node.js
- **Process Management**: Manual start/stop
- **Database**: MySQL on localhost:3506
- **File Storage**: Local screenshots directory

### System Requirements

**Server Specifications**:
- Node.js runtime environment
- MySQL database access (port 3506)
- Internet connectivity for Telegram API
- File system access for screenshot storage
- API access to company wallet service

### Monitoring & Logging

**Comprehensive Logging**:
- All user interactions logged
- Payment processing tracking
- Admin action audit trails
- Error logging with stack traces
- Performance monitoring

### Backup & Recovery

**Data Protection**:
- Regular database backups
- Screenshot file backups
- Configuration file versioning
- Disaster recovery procedures
- Data integrity checks

## Current Bot Workflow Examples

### Share Purchase Package Purchase Flow

1. **User Access**: User clicks "📦 Share Packages"
2. **Package Selection**: Bot displays 8 available packages with current phase info
3. **Terms Acceptance**: User must accept 6 different terms categories
4. **Payment Method**: Choose between crypto payment options
5. **Network Selection**: BSC USDT, POL USDT, or TRON USDT
6. **Wallet Display**: Bot shows company wallet address with copy button
7. **Payment Confirmation**: 3-step verification process
8. **Admin Approval**: Payment pending admin review
9. **Completion**: User receives confirmation and shares are allocated

### Custom Share Purchase Flow

1. **Amount Entry**: User enters custom amount ($25-$10,000)
2. **Package Optimization**: Bot calculates optimal package combination
3. **Confirmation**: User reviews package breakdown and total shares
4. **Terms & Payment**: Same process as package purchase
5. **Processing**: Share Purchase recorded with custom amount and calculated shares

### Admin Management Flow

1. **Admin Login**: Secure authentication for TTTFOUNDER
2. **Payment Review**: View pending payments with full details
3. **User Communication**: Direct messaging system with users
4. **Approval Process**: Approve/reject payments with notifications
5. **Audit Trail**: All actions logged with timestamps

## Future Enhancements

### Planned Features

**Mini Apps Integration**:
- Telegram Web Apps for advanced features
- Professional dashboard interface
- Enhanced portfolio visualization
- Advanced calculator tools

**Enhanced Communication**:
- Broadcast messaging system
- Automated notifications
- Multi-language support
- Rich media support

**Advanced Analytics**:
- Share Purchase performance tracking
- User behavior analytics
- Commission reporting
- Phase progression analytics

## Support & Maintenance

### User Support

**Help System**:
- Built-in help commands
- FAQ integration
- Direct admin communication
- Troubleshooting guides

### Technical Support

**Admin Tools**:
- Real-time monitoring dashboard
- Error reporting system
- User management interface
- System health checks

## Key Statistics & Performance

### Current Usage Metrics

**User Engagement**:
- Seamless authentication flow with 100% success rate
- Multi-step payment verification with admin approval
- Real-time phase tracking and availability
- Professional user interface with inline keyboards

**System Performance**:
- Instant response times for all commands
- Reliable database connectivity
- Robust error handling and recovery
- Comprehensive audit logging

### Business Impact

**Share Purchase Processing**:
- Complete share purchase package system (8 packages)
- Custom share purchase amounts ($25-$10,000)
- 20-phase share purchase progression system
- Real-time share calculations and allocations

**Payment Integration**:
- 3 cryptocurrency networks supported
- 3-step payment verification process
- Admin approval workflow
- Screenshot upload and storage

---

## Summary

The **Aureus Africa Bot** represents a complete share purchase platform integration, providing users with a seamless, secure, and professional way to participate in the Aureus Alliance Holdings mining equity opportunity directly through Telegram.

**Key Achievements**:
- ✅ Full authentication and user management system
- ✅ Complete share purchase package and custom share purchase flows
- ✅ Comprehensive payment processing with crypto support
- ✅ Admin management system with full audit trails
- ✅ Terms and conditions framework
- ✅ Mining calculator and dividend projections
- ✅ Referral system integration
- ✅ Professional user interface with button-based navigation

The bot serves as a powerful mobile-first interface that maintains full integration with the existing web platform while providing users with convenient access to all share purchase features directly within Telegram.

*This documentation reflects the current state of the fully functional Telegram bot as of the latest deployment.*
