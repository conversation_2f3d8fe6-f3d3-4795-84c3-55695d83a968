const { db } = require('./src/database/supabase-client');

async function fixCommissionConstraint() {
  console.log('🔧 Fixing commission_balances constraint issue...\n');

  try {
    // Step 1: Check current constraints
    console.log('📋 Checking current constraints...');
    const { data: constraints, error: constraintsError } = await db.client
      .rpc('exec_sql', {
        sql: `
          SELECT 
              constraint_name,
              constraint_type
          FROM information_schema.table_constraints 
          WHERE table_name = 'commission_balances';
        `
      });

    if (constraintsError) {
      console.log('⚠️ Could not check constraints:', constraintsError.message);
    } else {
      console.log('Current constraints:', constraints);
    }

    // Step 2: Try to identify the problematic commission conversion
    console.log('\n🔍 Checking commission conversion with ID 374781cb...');
    const { data: conversion, error: conversionError } = await db.client
      .from('commission_conversions')
      .select('*')
      .ilike('id', '%374781cb%')
      .single();

    if (conversionError) {
      console.log('⚠️ Could not find commission conversion:', conversionError.message);
    } else {
      console.log('📊 Commission conversion details:', JSON.stringify(conversion, null, 2));
      
      // Check the user's commission balance
      const { data: balance, error: balanceError } = await db.client
        .from('commission_balances')
        .select('*')
        .eq('user_id', conversion.user_id)
        .single();

      if (balanceError) {
        console.log('⚠️ Could not find user balance:', balanceError.message);
      } else {
        console.log('💰 User commission balance:', JSON.stringify(balance, null, 2));
        
        // Calculate what the balance should be after conversion
        const currentUSDT = parseFloat(balance.usdt_balance || 0);
        const conversionAmount = parseFloat(conversion.usdt_amount || 0);
        const currentEscrow = parseFloat(balance.escrowed_amount || 0);
        
        console.log('\n📊 Balance Analysis:');
        console.log(`Current USDT Balance: $${currentUSDT}`);
        console.log(`Conversion Amount: $${conversionAmount}`);
        console.log(`Current Escrow: $${currentEscrow}`);
        console.log(`Available Balance: $${currentUSDT - currentEscrow}`);
        
        if (currentUSDT - currentEscrow < conversionAmount) {
          console.log('❌ ISSUE: Insufficient available balance for conversion');
          console.log('🔧 The constraint is working correctly - user doesn\'t have enough balance');
        }
      }
    }

    // Step 3: Check if we can manually fix the balance issue
    console.log('\n🔧 Attempting to fix commission balance constraint...');
    
    // First, let's see the exact error by trying a simple update
    const { data: testUpdate, error: updateError } = await db.client
      .from('commission_balances')
      .update({ updated_at: new Date().toISOString() })
      .eq('user_id', 88); // User ID from the error

    if (updateError) {
      console.log('❌ Update error details:', JSON.stringify(updateError, null, 2));
      console.log('\n🎯 SOLUTION NEEDED:');
      console.log('1. The constraint "check_sufficient_balance" is preventing commission balance updates');
      console.log('2. This constraint needs to be removed or modified');
      console.log('3. Run the SQL file fix-commission-constraint.sql in your Supabase SQL editor');
      console.log('\n📄 SQL Fix Required:');
      console.log('-- Execute this in Supabase SQL Editor:');
      console.log('ALTER TABLE commission_balances DROP CONSTRAINT IF EXISTS check_sufficient_balance;');
    } else {
      console.log('✅ Commission balance update successful');
    }

  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run the fix
fixCommissionConstraint().then(() => {
  console.log('\n✅ Commission constraint analysis completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
