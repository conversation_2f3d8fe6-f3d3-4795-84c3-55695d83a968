-- Safe Migration: Add is_admin column to users table
-- This script safely adds the is_admin column for Task 2 admin verification
-- Production-safe: Uses IF NOT EXISTS and safe defaults

-- ============================================================================
-- STEP 1: Add is_admin column to users table (safe operation)
-- ============================================================================

-- Add is_admin column with safe default (false for all existing users)
ALTER TABLE public.users 
ADD COLUMN IF NOT EXISTS is_admin BOOLEAN DEFAULT FALSE;

-- ============================================================================
-- STEP 2: Set TTTFOUNDER as admin (required for Task 2)
-- ============================================================================

-- Update TTTFOUNDER to be admin (safe - only affects one user)
UPDATE public.users 
SET is_admin = TRUE 
WHERE username = 'TTTFOUNDER';

-- ============================================================================
-- STEP 3: Create index for performance (optional but recommended)
-- ============================================================================

-- Add index on is_admin column for faster admin queries
CREATE INDEX IF NOT EXISTS idx_users_is_admin ON public.users(is_admin) 
WHERE is_admin = TRUE;

-- ============================================================================
-- STEP 4: Verification queries (run these to confirm success)
-- ============================================================================

-- Check that column was added successfully
-- SELECT column_name, data_type, is_nullable, column_default 
-- FROM information_schema.columns 
-- WHERE table_name = 'users' AND column_name = 'is_admin';

-- Check that TTTFOUNDER is now admin
-- SELECT username, is_admin, created_at 
-- FROM public.users 
-- WHERE username = 'TTTFOUNDER';

-- Check total admin count (should be 1)
-- SELECT COUNT(*) as admin_count 
-- FROM public.users 
-- WHERE is_admin = TRUE;

-- ============================================================================
-- ROLLBACK PLAN (if needed)
-- ============================================================================

-- If you need to rollback this migration:
-- ALTER TABLE public.users DROP COLUMN IF EXISTS is_admin;
-- DROP INDEX IF EXISTS idx_users_is_admin;

-- ============================================================================
-- NOTES
-- ============================================================================

-- 1. This migration is SAFE for production:
--    - Uses IF NOT EXISTS to prevent errors if column already exists
--    - Sets safe default (FALSE) for all existing users
--    - Only promotes TTTFOUNDER to admin
--    - Creates performance index

-- 2. After running this migration:
--    - Task 2 admin verification will work
--    - Only TTTFOUNDER will have admin access
--    - All other users will have is_admin = FALSE

-- 3. To add more admins in the future:
--    UPDATE public.users SET is_admin = TRUE WHERE username = 'NEW_ADMIN_USERNAME';
