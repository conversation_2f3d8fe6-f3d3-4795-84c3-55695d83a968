const { db } = require('./src/database/supabase-client');

async function fixCommissionEscrowIssue() {
  console.log('🔧 Fixing Commission Escrow and Constraint Issue...\n');

  try {
    // Step 1: Check user 88's current balance
    console.log('📊 Checking user 88 commission balance...');
    const { data: balance, error: balanceError } = await db.client
      .from('commission_balances')
      .select('*')
      .eq('user_id', 88)
      .single();

    if (balanceError) {
      console.log('❌ Could not find user balance:', balanceError.message);
      return;
    }

    console.log('💰 Current balance:', JSON.stringify(balance, null, 2));

    const usdtBalance = parseFloat(balance.usdt_balance || 0);
    const escrowedAmount = parseFloat(balance.escrowed_amount || 0);
    const availableBalance = usdtBalance - escrowedAmount;

    console.log(`\n📈 Balance Analysis:`);
    console.log(`USDT Balance: $${usdtBalance}`);
    console.log(`Escrowed: $${escrowedAmount}`);
    console.log(`Available: $${availableBalance}`);

    if (escrowedAmount > 0 && availableBalance <= 0) {
      console.log('🚨 ISSUE DETECTED: All funds are escrowed, user cannot perform conversions');
      
      // Step 2: Check for pending commission conversions
      console.log('\n🔍 Checking for pending commission conversions...');
      const { data: conversions, error: conversionError } = await db.client
        .from('commission_conversions')
        .select('*')
        .eq('user_id', 88)
        .eq('status', 'pending');

      if (conversionError) {
        console.log('⚠️ Could not check conversions:', conversionError.message);
      } else {
        console.log(`📋 Found ${conversions.length} pending conversions`);
        if (conversions.length > 0) {
          console.log('Conversion details:', JSON.stringify(conversions, null, 2));
        }
      }

      // Step 3: Release the escrow to fix the issue
      console.log('\n🔧 Releasing escrow to fix the deadlock...');
      const { data: fixResult, error: fixError } = await db.client
        .from('commission_balances')
        .update({
          escrowed_amount: 0.00,
          last_updated: new Date().toISOString()
        })
        .eq('user_id', 88);

      if (fixError) {
        console.log('❌ Could not release escrow:', JSON.stringify(fixError, null, 2));
        
        // The constraint is likely the issue - provide manual SQL fix
        console.log('\n🎯 MANUAL FIX REQUIRED:');
        console.log('The database constraint is preventing the update. Execute this SQL in Supabase:');
        console.log('```sql');
        console.log('-- Remove the problematic constraint');
        console.log('ALTER TABLE commission_balances DROP CONSTRAINT IF EXISTS check_sufficient_balance;');
        console.log('');
        console.log('-- Fix the stuck escrow');
        console.log('UPDATE commission_balances SET escrowed_amount = 0.00 WHERE user_id = 88;');
        console.log('');
        console.log('-- Add a better constraint');
        console.log('ALTER TABLE commission_balances ADD CONSTRAINT check_non_negative_balances');
        console.log('CHECK (usdt_balance >= 0 AND escrowed_amount >= 0 AND total_withdrawn >= 0);');
        console.log('```');
      } else {
        console.log('✅ Escrow released successfully');
        
        // Verify the fix
        const { data: updatedBalance } = await db.client
          .from('commission_balances')
          .select('*')
          .eq('user_id', 88)
          .single();

        console.log('✅ Updated balance:', JSON.stringify(updatedBalance, null, 2));
      }
    } else {
      console.log('✅ No escrow issue detected');
    }

    // Step 4: Check for other users with similar issues
    console.log('\n🔍 Checking for other users with escrow issues...');
    const { data: problematicBalances, error: checkError } = await db.client
      .from('commission_balances')
      .select('user_id, usdt_balance, escrowed_amount')
      .gt('escrowed_amount', 0);

    if (checkError) {
      console.log('⚠️ Could not check other balances:', checkError.message);
    } else {
      const issues = problematicBalances.filter(b => 
        parseFloat(b.usdt_balance) <= parseFloat(b.escrowed_amount)
      );
      
      if (issues.length > 0) {
        console.log(`⚠️ Found ${issues.length} other users with escrow issues:`);
        issues.forEach(issue => {
          console.log(`User ${issue.user_id}: Balance $${issue.usdt_balance}, Escrowed $${issue.escrowed_amount}`);
        });
      } else {
        console.log('✅ No other users have escrow issues');
      }
    }

  } catch (error) {
    console.error('❌ Script error:', error);
  }
}

// Run the fix
fixCommissionEscrowIssue().then(() => {
  console.log('\n✅ Commission escrow fix completed');
  process.exit(0);
}).catch(error => {
  console.error('❌ Fatal error:', error);
  process.exit(1);
});
