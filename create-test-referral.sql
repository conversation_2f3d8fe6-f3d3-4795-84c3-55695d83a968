-- Create a test referral for TTTFOUNDER (user_id: 116) to test the referral system
-- This will create a dummy referral so you can see the referral counting working

-- First, let's see what users exist that we can use as referred users
SELECT id, username, full_name, email FROM users WHERE id != 116 LIMIT 5;

-- Create a test referral (assuming user_id 1 exists as a referred user)
-- You may need to adjust the referred_id based on what users exist in your database
INSERT INTO referrals (
  referrer_id,
  referred_id,
  referral_code,
  commission_rate,
  status,
  created_at
) VALUES (
  116,  -- TTTFOUNDER's user_id
  1,    -- Some other user as the referred user (adjust this if needed)
  'TTTFOUNDER_TEST_' || extract(epoch from now()),
  15.00,
  'active',
  NOW()
);

-- Verify the test referral was created
SELECT 
  r.*,
  u.username as referred_username,
  u.full_name as referred_name
FROM referrals r
LEFT JOIN users u ON r.referred_id = u.id
WHERE r.referrer_id = 116;
