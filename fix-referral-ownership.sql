-- Fix referral ownership to point to TTTFOUNDER's current user_id
-- This will update all referrals that currently have referrer_id = 4 
-- to use referrer_id = 116 (TTTFOUNDER's current user_id)

-- First, let's see what we're updating
SELECT 
  id,
  referrer_id,
  referred_id,
  status,
  created_at
FROM referrals 
WHERE referrer_id = 4;

-- Update the referrals to point to the correct user_id
UPDATE referrals 
SET referrer_id = 116 
WHERE referrer_id = 4;

-- Verify the update
SELECT 
  id,
  referrer_id,
  referred_id,
  status,
  created_at
FROM referrals 
WHERE referrer_id = 116;

-- Also update any commission transactions that might reference the old referrer_id
UPDATE commission_transactions 
SET referrer_id = 116 
WHERE referrer_id = 4;

-- Update commission balances if they exist for the old user_id
UPDATE commission_balances 
SET user_id = 116 
WHERE user_id = 4;
