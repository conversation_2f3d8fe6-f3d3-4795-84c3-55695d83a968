// Fix User 88 Escrow Issue
// The admin approved the commission conversion but escrow wasn't cleared

require('dotenv').config();
const { createClient } = require('@supabase/supabase-js');
const supabase = createClient(process.env.SUPABASE_URL, process.env.SUPABASE_SERVICE_ROLE_KEY);

async function fixUser88Escrow() {
  console.log('🔧 Fixing User 88 escrow issue...');
  
  try {
    // Check current status
    const { data: currentBalance, error: balanceError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', 88)
      .single();
    
    if (balanceError) {
      console.error('❌ Error getting current balance:', balanceError);
      return;
    }
    
    console.log('📊 Current balance:', currentBalance);
    
    // Check if conversion was approved
    const { data: conversion, error: convError } = await supabase
      .from('commission_conversions')
      .select('*')
      .eq('user_id', 88)
      .eq('status', 'approved')
      .order('created_at', { ascending: false })
      .limit(1);
    
    if (convError || !conversion || conversion.length === 0) {
      console.error('❌ No approved conversion found for User 88');
      return;
    }
    
    console.log('✅ Found approved conversion:', conversion[0]);
    
    const approvedConversion = conversion[0];
    const convertedAmount = parseFloat(approvedConversion.usdt_amount);
    
    // Clear the escrow (should be 0 since conversion was approved)
    console.log(`🔒 Clearing escrow amount: $${currentBalance.escrowed_amount}`);
    
    const { error: updateError } = await supabase
      .from('commission_balances')
      .update({
        escrowed_amount: 0,
        last_updated: new Date().toISOString()
      })
      .eq('user_id', 88);
    
    if (updateError) {
      console.error('❌ Error clearing escrow:', updateError);
      return;
    }
    
    console.log('✅ Escrow cleared successfully!');
    
    // Verify the fix
    const { data: updatedBalance, error: verifyError } = await supabase
      .from('commission_balances')
      .select('*')
      .eq('user_id', 88)
      .single();
    
    if (!verifyError) {
      console.log('📊 Updated balance:', updatedBalance);
      console.log(`✅ Fixed: Escrow amount now $${updatedBalance.escrowed_amount} (was $${currentBalance.escrowed_amount})`);
    }
    
  } catch (error) {
    console.error('❌ Error fixing escrow:', error);
  }
}

fixUser88Escrow();
